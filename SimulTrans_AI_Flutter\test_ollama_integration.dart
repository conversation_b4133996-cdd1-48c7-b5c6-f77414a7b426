#!/usr/bin/env dart

/// Test script for Ollama integration with SimulTrans AI Flutter
/// 
/// This script validates the complete migration to Ollama with Gemma-3N
/// Run with: dart test_ollama_integration.dart

import 'dart:io';
import 'dart:convert';
import 'dart:typed_data';

void main() async {
  print('🧪 Testing Ollama Integration with SimulTrans AI Flutter');
  print('=' * 60);
  
  await testOllamaConnection();
  await testOllamaModels();
  await testTextTranslation();
  await testImageTranslation();
  
  print('\n🎉 All tests completed!');
}

/// Test Ollama server connection
Future<void> testOllamaConnection() async {
  print('\n1. 🔌 Testing Ollama Connection...');
  
  try {
    final client = HttpClient();
    final request = await client.getUrl(Uri.parse('http://localhost:11434/api/tags'));
    request.headers.set('Content-Type', 'application/json');
    
    final response = await request.close();
    
    if (response.statusCode == 200) {
      print('   ✅ Ollama server is running on http://localhost:11434');
    } else {
      print('   ❌ Ollama server returned status: ${response.statusCode}');
      print('   💡 Make sure Ollama is running: ollama serve');
    }
    
    client.close();
  } catch (e) {
    print('   ❌ Cannot connect to Ollama: $e');
    print('   💡 Make sure Ollama is installed and running');
    print('   💡 Download from: https://ollama.ai/download');
  }
}

/// Test available Gemma-3N models
Future<void> testOllamaModels() async {
  print('\n2. 🧠 Testing Available Models...');
  
  try {
    final client = HttpClient();
    final request = await client.getUrl(Uri.parse('http://localhost:11434/api/tags'));
    request.headers.set('Content-Type', 'application/json');
    
    final response = await request.close();
    final responseBody = await response.transform(utf8.decoder).join();
    
    if (response.statusCode == 200) {
      final data = json.decode(responseBody);
      final models = data['models'] as List<dynamic>? ?? [];
      
      print('   📋 Available models:');
      bool hasGemma3n = false;
      
      for (final model in models) {
        final modelName = model['name'] as String;
        print('      - $modelName');
        
        if (modelName.contains('gemma3n')) {
          hasGemma3n = true;
          print('        ✅ Gemma-3N model found!');
        }
      }
      
      if (!hasGemma3n) {
        print('   ⚠️  No Gemma-3N models found');
        print('   💡 Install with: ollama pull gemma3n:e2b');
        print('   💡 Or: ollama pull gemma3n:e4b');
      }
    } else {
      print('   ❌ Failed to get models: ${response.statusCode}');
    }
    
    client.close();
  } catch (e) {
    print('   ❌ Error getting models: $e');
  }
}

/// Test text translation
Future<void> testTextTranslation() async {
  print('\n3. 📝 Testing Text Translation...');
  
  try {
    final client = HttpClient();
    final request = await client.postUrl(Uri.parse('http://localhost:11434/api/chat'));
    request.headers.set('Content-Type', 'application/json');
    
    final payload = {
      'model': 'gemma3n:e2b',
      'messages': [
        {
          'role': 'system',
          'content': 'You are a professional translator. Provide only the translation without explanations.',
        },
        {
          'role': 'user',
          'content': 'Translate the following text from English to Portuguese:\n\nHello world\n\nTranslation:',
        }
      ],
      'stream': false,
      'options': {
        'temperature': 0.1,
        'num_predict': 100,
      },
    };
    
    request.write(json.encode(payload));
    final response = await request.close();
    final responseBody = await response.transform(utf8.decoder).join();
    
    if (response.statusCode == 200) {
      final data = json.decode(responseBody);
      final message = data['message'];
      
      if (message != null && message['content'] != null) {
        final translation = message['content'] as String;
        print('   ✅ Translation successful!');
        print('   📝 Input: "Hello world"');
        print('   🎯 Output: "${translation.trim()}"');
        
        // Basic validation
        if (translation.toLowerCase().contains('olá') || 
            translation.toLowerCase().contains('mundo')) {
          print('   ✅ Translation quality: Good');
        } else {
          print('   ⚠️  Translation quality: Needs review');
        }
      } else {
        print('   ❌ Invalid response format');
      }
    } else {
      print('   ❌ Translation failed: ${response.statusCode}');
      print('   📄 Response: $responseBody');
    }
    
    client.close();
  } catch (e) {
    print('   ❌ Error during text translation: $e');
  }
}

/// Test image translation (mock test)
Future<void> testImageTranslation() async {
  print('\n4. 🖼️  Testing Image Translation Support...');
  
  try {
    // Create a simple test image (1x1 pixel PNG)
    final testImageBytes = base64Decode(
      'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAGA'
    );
    final base64Image = base64Encode(testImageBytes);
    
    final client = HttpClient();
    final request = await client.postUrl(Uri.parse('http://localhost:11434/api/chat'));
    request.headers.set('Content-Type', 'application/json');
    
    final payload = {
      'model': 'gemma3n:e2b',
      'messages': [
        {
          'role': 'system',
          'content': 'You are a professional translator. Extract text from images and translate it.',
        },
        {
          'role': 'user',
          'content': 'Extract any text from this image and translate it to Portuguese.',
          'images': [base64Image],
        }
      ],
      'stream': false,
      'options': {
        'temperature': 0.1,
        'num_predict': 100,
      },
    };
    
    request.write(json.encode(payload));
    final response = await request.close();
    final responseBody = await response.transform(utf8.decoder).join();
    
    if (response.statusCode == 200) {
      final data = json.decode(responseBody);
      final message = data['message'];
      
      if (message != null && message['content'] != null) {
        print('   ✅ Image processing successful!');
        print('   🖼️  Model can process images');
        print('   📝 Response: "${message['content']}"');
      } else {
        print('   ❌ Invalid response format for image');
      }
    } else {
      print('   ❌ Image translation failed: ${response.statusCode}');
      if (responseBody.contains('image input modality is not enabled')) {
        print('   ⚠️  Model does not support image input');
        print('   💡 Try using a multimodal Gemma-3N variant');
      }
    }
    
    client.close();
  } catch (e) {
    print('   ❌ Error during image translation: $e');
  }
}

/// Utility function to create test image bytes
Uint8List createTestImageBytes() {
  // Simple 1x1 pixel PNG in base64
  const pngBase64 = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAGA';
  return base64Decode(pngBase64);
}

/// Print test results summary
void printTestSummary() {
  print('\n📊 Test Summary:');
  print('   🔌 Ollama Connection: Check logs above');
  print('   🧠 Model Availability: Check logs above');
  print('   📝 Text Translation: Check logs above');
  print('   🖼️  Image Translation: Check logs above');
  print('\n💡 Next Steps:');
  print('   1. Ensure Ollama is running: ollama serve');
  print('   2. Install Gemma-3N: ollama pull gemma3n:e2b');
  print('   3. Test Flutter app with new Ollama integration');
  print('   4. Check OLLAMA_MIGRATION_GUIDE.md for detailed setup');
}
