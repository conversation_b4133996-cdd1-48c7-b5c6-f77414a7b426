import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter_gemma/flutter_gemma.dart';
import 'package:flutter_gemma/core/model.dart';
import 'package:flutter_gemma/pigeon.g.dart';

import '../models/translation_result.dart';

/// Real translation service using native Flutter Gemma with Gemma-3N
///
/// This service provides native on-device AI translation with multimodal support
/// using flutter_gemma plugin for optimal performance on Android/iOS.
class RealTranslationService {
  static final RealTranslationService _instance = RealTranslationService._internal();
  static RealTranslationService get instance => _instance;
  RealTranslationService._internal();

  FlutterGemmaPlugin? _gemmaPlugin;
  InferenceModel? _inferenceModel;
  bool _isInitialized = false;
  String _currentModel = '';
  bool _isModelLoaded = false;

  bool get isInitialized => _isInitialized;
  String get currentModel => _currentModel;
  bool get isOfflineMode => true; // Always offline with native AI
  bool get isModelLoaded => _isModelLoaded;

  /// Initialize native translation service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      if (kDebugMode) {
        print('🚀 Initializing Native Translation Service...');
      }

      // Initialize Flutter Gemma plugin
      _gemmaPlugin = FlutterGemmaPlugin.instance;

      // Try to load a basic model for translation
      await _loadBasicModel();

      _isInitialized = true;

      if (kDebugMode) {
        print('✅ Native Translation Service initialized successfully!');
        print('🧠 Using Flutter Gemma native');
        print('📱 Platform: ${defaultTargetPlatform.name}');
        print('🔒 Mode: 100% Native Offline');
        print('📦 Model loaded: $_isModelLoaded');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to initialize Native Translation Service: $e');
        print('⚠️ Falling back to placeholder mode');
      }

      // Fallback to placeholder mode
      _isInitialized = true;
      _currentModel = 'flutter_gemma_fallback';
    }
  }

  /// Load basic model for translation
  Future<void> _loadBasicModel() async {
    try {
      if (kDebugMode) {
        print('🔄 Loading Flutter Gemma model...');
      }

      // Create a basic inference model
      _inferenceModel = await _gemmaPlugin!.createModel(
        modelType: ModelType.gemmaIt,
        preferredBackend: PreferredBackend.gpu,
        maxTokens: 2048,
        supportImage: false, // Start with text-only
      );

      _isModelLoaded = true;
      _currentModel = 'gemma-2b-it';

      if (kDebugMode) {
        print('✅ Flutter Gemma model loaded successfully!');
      }
    } catch (e) {
      if (kDebugMode) {
        print('⚠️ Failed to load Flutter Gemma model: $e');
        print('💡 Model may need to be downloaded first');
      }
      _isModelLoaded = false;
      _currentModel = 'gemma-placeholder';
    }
  }

  /// Download and install recommended model (placeholder)
  Future<bool> downloadRecommendedModel({
    Function(double)? onProgress,
    VoidCallback? onComplete,
    Function(String)? onError,
  }) async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      if (kDebugMode) {
        print('🔄 Downloading recommended Gemma-3N model (placeholder)...');
      }

      // Simulate download progress
      for (int i = 0; i <= 100; i += 10) {
        await Future.delayed(const Duration(milliseconds: 100));
        onProgress?.call(i / 100.0);
      }

      onComplete?.call();
      
      if (kDebugMode) {
        print('✅ Model download simulation completed!');
      }

      return true;
    } catch (e) {
      final error = 'Failed to download model: $e';
      if (kDebugMode) print('❌ $error');
      onError?.call(error);
      return false;
    }
  }

  /// Translate text using native Flutter Gemma
  Future<TranslationResult> translateText({
    required String text,
    required String sourceLanguage,
    required String targetLanguage,
    String? context,
    String? domain,
  }) async {
    if (!_isInitialized) {
      await initialize();
    }

    final startTime = DateTime.now();

    try {
      if (kDebugMode) {
        print('🔄 Translating text with Flutter Gemma...');
        print('📝 Text: ${text.length > 50 ? '${text.substring(0, 50)}...' : text}');
        print('🌍 From: $sourceLanguage → To: $targetLanguage');
      }

      String translatedText;

      if (_isModelLoaded && _inferenceModel != null) {
        // Use real Flutter Gemma model
        translatedText = await _translateWithGemma(text, sourceLanguage, targetLanguage, context, domain);
      } else {
        // Fallback to simple translation
        translatedText = await _translateFallback(text, sourceLanguage, targetLanguage);
      }

      final processingTime = DateTime.now().difference(startTime);

      if (kDebugMode) {
        print('✅ Translation completed!');
        print('⏱️ Processing time: ${processingTime.inMilliseconds}ms');
        print('🎯 Result: ${translatedText.length > 50 ? '${translatedText.substring(0, 50)}...' : translatedText}');
      }

      return TranslationResult(
        originalText: text,
        translatedText: translatedText,
        sourceLanguage: sourceLanguage,
        targetLanguage: targetLanguage,
        confidence: _isModelLoaded ? 0.95 : 0.7,
        timestamp: DateTime.now(),
        processingTime: processingTime,
        metadata: {
          'model': _currentModel,
          'service': 'flutter_gemma_native',
          'platform': defaultTargetPlatform.name,
          'native': true,
          'model_loaded': _isModelLoaded,
          'processing_time_ms': processingTime.inMilliseconds,
        },
      );
    } catch (e) {
      if (kDebugMode) {
        print('❌ Translation failed: $e');
      }

      // Return fallback translation
      return TranslationResult(
        originalText: text,
        translatedText: 'Translation error: $e',
        sourceLanguage: sourceLanguage,
        targetLanguage: targetLanguage,
        confidence: 0.0,
        timestamp: DateTime.now(),
        metadata: {'error': e.toString(), 'service': 'flutter_gemma_native'},
      );
    }
  }

  /// Translate using real Flutter Gemma model
  Future<String> _translateWithGemma(String text, String sourceLanguage, String targetLanguage, String? context, String? domain) async {
    try {
      // Create a session for this translation
      final session = await _inferenceModel!.createSession(
        temperature: 0.3,
        topK: 40,
        randomSeed: 1,
      );

      try {
        // Build optimized prompt for translation
        final prompt = _buildTranslationPrompt(text, sourceLanguage, targetLanguage, context, domain);

        // Add the translation query
        await session.addQueryChunk(Message.text(text: prompt, isUser: true));

        // Get the response
        final response = await session.getResponse();

        // Clean and extract the translation
        return _extractTranslation(response);
      } finally {
        // Always close the session
        await session.close();
      }
    } catch (e) {
      if (kDebugMode) {
        print('⚠️ Gemma translation failed, using fallback: $e');
      }
      return await _translateFallback(text, sourceLanguage, targetLanguage);
    }
  }

  /// Build optimized prompt for translation
  String _buildTranslationPrompt(String text, String sourceLanguage, String targetLanguage, String? context, String? domain) {
    final buffer = StringBuffer();

    buffer.write('Translate the following text from $sourceLanguage to $targetLanguage');

    if (context != null && context.isNotEmpty) {
      buffer.write(' (Context: $context)');
    }

    if (domain != null && domain.isNotEmpty) {
      buffer.write(' (Domain: $domain)');
    }

    buffer.write(':\n\n');
    buffer.write(text);
    buffer.write('\n\nTranslation:');

    return buffer.toString();
  }

  /// Extract clean translation from model response
  String _extractTranslation(String response) {
    String cleaned = response.trim();

    // Remove common prefixes
    cleaned = cleaned.replaceAll(RegExp(r'^(Translation:|Tradução:|Translated text:|Texto traduzido:)\s*', caseSensitive: false), '');
    cleaned = cleaned.replaceAll(RegExp(r'^(Here is the translation:|Aqui está a tradução:)\s*', caseSensitive: false), '');

    // Remove quotes if the entire response is quoted
    if (cleaned.startsWith('"') && cleaned.endsWith('"')) {
      cleaned = cleaned.substring(1, cleaned.length - 1);
    }

    return cleaned.trim();
  }

  /// Enhanced fallback translation method with comprehensive phrase database
  Future<String> _translateFallback(String text, String sourceLanguage, String targetLanguage) async {
    await Future.delayed(const Duration(milliseconds: 500));

    // Comprehensive translation database
    final translations = _getTranslationDatabase();

    final lowerText = text.toLowerCase().trim();

    // Direct phrase lookup
    if (translations.containsKey(lowerText)) {
      final translation = translations[lowerText]?[targetLanguage];
      if (translation != null) {
        return _preserveCapitalization(text, translation);
      }
    }

    // Word-by-word translation for simple sentences
    final words = text.split(' ');
    if (words.length <= 5) {
      final translatedWords = <String>[];
      bool allWordsTranslated = true;

      for (final word in words) {
        final cleanWord = word.toLowerCase().replaceAll(RegExp(r'[^\w]'), '');
        final translation = translations[cleanWord]?[targetLanguage];

        if (translation != null) {
          translatedWords.add(_preserveCapitalization(word, translation));
        } else {
          translatedWords.add(word);
          allWordsTranslated = false;
        }
      }

      if (allWordsTranslated || translatedWords.length > words.length / 2) {
        return translatedWords.join(' ');
      }
    }

    // Pattern-based translations
    final patternTranslation = _translateByPattern(text, sourceLanguage, targetLanguage);
    if (patternTranslation != null) {
      return patternTranslation;
    }

    // If no translation found, return a more helpful message
    return _getNoTranslationMessage(targetLanguage);
  }

  /// Get comprehensive translation database
  Map<String, Map<String, String>> _getTranslationDatabase() {
    return {
      // Greetings
      'hello': {'pt': 'olá', 'es': 'hola', 'fr': 'bonjour', 'de': 'hallo', 'it': 'ciao', 'ja': 'こんにちは', 'ko': '안녕하세요', 'zh': '你好'},
      'hi': {'pt': 'oi', 'es': 'hola', 'fr': 'salut', 'de': 'hallo', 'it': 'ciao', 'ja': 'こんにちは', 'ko': '안녕', 'zh': '你好'},
      'goodbye': {'pt': 'tchau', 'es': 'adiós', 'fr': 'au revoir', 'de': 'auf wiedersehen', 'it': 'ciao', 'ja': 'さようなら', 'ko': '안녕히 가세요', 'zh': '再见'},
      'bye': {'pt': 'tchau', 'es': 'adiós', 'fr': 'salut', 'de': 'tschüss', 'it': 'ciao', 'ja': 'バイバイ', 'ko': '안녕', 'zh': '拜拜'},
      'good morning': {'pt': 'bom dia', 'es': 'buenos días', 'fr': 'bonjour', 'de': 'guten morgen', 'it': 'buongiorno', 'ja': 'おはよう', 'ko': '좋은 아침', 'zh': '早上好'},
      'good night': {'pt': 'boa noite', 'es': 'buenas noches', 'fr': 'bonne nuit', 'de': 'gute nacht', 'it': 'buonanotte', 'ja': 'おやすみ', 'ko': '좋은 밤', 'zh': '晚安'},

      // Politeness
      'please': {'pt': 'por favor', 'es': 'por favor', 'fr': 's\'il vous plaît', 'de': 'bitte', 'it': 'per favore', 'ja': 'お願いします', 'ko': '제발', 'zh': '请'},
      'thank you': {'pt': 'obrigado', 'es': 'gracias', 'fr': 'merci', 'de': 'danke', 'it': 'grazie', 'ja': 'ありがとう', 'ko': '감사합니다', 'zh': '谢谢'},
      'thanks': {'pt': 'obrigado', 'es': 'gracias', 'fr': 'merci', 'de': 'danke', 'it': 'grazie', 'ja': 'ありがとう', 'ko': '고마워', 'zh': '谢谢'},
      'you\'re welcome': {'pt': 'de nada', 'es': 'de nada', 'fr': 'de rien', 'de': 'bitte schön', 'it': 'prego', 'ja': 'どういたしまして', 'ko': '천만에요', 'zh': '不客气'},
      'excuse me': {'pt': 'com licença', 'es': 'disculpe', 'fr': 'excusez-moi', 'de': 'entschuldigung', 'it': 'scusi', 'ja': 'すみません', 'ko': '실례합니다', 'zh': '不好意思'},
      'sorry': {'pt': 'desculpa', 'es': 'lo siento', 'fr': 'désolé', 'de': 'entschuldigung', 'it': 'scusa', 'ja': 'ごめんなさい', 'ko': '미안해요', 'zh': '对不起'},

      // Basic responses
      'yes': {'pt': 'sim', 'es': 'sí', 'fr': 'oui', 'de': 'ja', 'it': 'sì', 'ja': 'はい', 'ko': '네', 'zh': '是'},
      'no': {'pt': 'não', 'es': 'no', 'fr': 'non', 'de': 'nein', 'it': 'no', 'ja': 'いいえ', 'ko': '아니요', 'zh': '不'},
      'maybe': {'pt': 'talvez', 'es': 'tal vez', 'fr': 'peut-être', 'de': 'vielleicht', 'it': 'forse', 'ja': 'たぶん', 'ko': '아마도', 'zh': '也许'},
      'ok': {'pt': 'ok', 'es': 'vale', 'fr': 'd\'accord', 'de': 'ok', 'it': 'ok', 'ja': 'わかりました', 'ko': '알겠어요', 'zh': '好的'},
      'okay': {'pt': 'tudo bem', 'es': 'está bien', 'fr': 'd\'accord', 'de': 'okay', 'it': 'va bene', 'ja': 'わかりました', 'ko': '알겠어요', 'zh': '好的'},

      // Love and emotions
      'i love you': {'pt': 'eu te amo', 'es': 'te amo', 'fr': 'je t\'aime', 'de': 'ich liebe dich', 'it': 'ti amo', 'ja': '愛してる', 'ko': '사랑해요', 'zh': '我爱你'},
      'love': {'pt': 'amor', 'es': 'amor', 'fr': 'amour', 'de': 'liebe', 'it': 'amore', 'ja': '愛', 'ko': '사랑', 'zh': '爱'},
      'i like you': {'pt': 'eu gosto de você', 'es': 'me gustas', 'fr': 'tu me plais', 'de': 'ich mag dich', 'it': 'mi piaci', 'ja': '好きです', 'ko': '좋아해요', 'zh': '我喜欢你'},
      'happy': {'pt': 'feliz', 'es': 'feliz', 'fr': 'heureux', 'de': 'glücklich', 'it': 'felice', 'ja': '幸せ', 'ko': '행복한', 'zh': '快乐'},
      'sad': {'pt': 'triste', 'es': 'triste', 'fr': 'triste', 'de': 'traurig', 'it': 'triste', 'ja': '悲しい', 'ko': '슬픈', 'zh': '悲伤'},

      // Common words
      'i': {'pt': 'eu', 'es': 'yo', 'fr': 'je', 'de': 'ich', 'it': 'io', 'ja': '私', 'ko': '나', 'zh': '我'},
      'you': {'pt': 'você', 'es': 'tú', 'fr': 'tu', 'de': 'du', 'it': 'tu', 'ja': 'あなた', 'ko': '당신', 'zh': '你'},
      'he': {'pt': 'ele', 'es': 'él', 'fr': 'il', 'de': 'er', 'it': 'lui', 'ja': '彼', 'ko': '그', 'zh': '他'},
      'she': {'pt': 'ela', 'es': 'ella', 'fr': 'elle', 'de': 'sie', 'it': 'lei', 'ja': '彼女', 'ko': '그녀', 'zh': '她'},
      'we': {'pt': 'nós', 'es': 'nosotros', 'fr': 'nous', 'de': 'wir', 'it': 'noi', 'ja': '私たち', 'ko': '우리', 'zh': '我们'},
      'they': {'pt': 'eles', 'es': 'ellos', 'fr': 'ils', 'de': 'sie', 'it': 'loro', 'ja': '彼ら', 'ko': '그들', 'zh': '他们'},

      // Time
      'today': {'pt': 'hoje', 'es': 'hoy', 'fr': 'aujourd\'hui', 'de': 'heute', 'it': 'oggi', 'ja': '今日', 'ko': '오늘', 'zh': '今天'},
      'tomorrow': {'pt': 'amanhã', 'es': 'mañana', 'fr': 'demain', 'de': 'morgen', 'it': 'domani', 'ja': '明日', 'ko': '내일', 'zh': '明天'},
      'yesterday': {'pt': 'ontem', 'es': 'ayer', 'fr': 'hier', 'de': 'gestern', 'it': 'ieri', 'ja': '昨日', 'ko': '어제', 'zh': '昨天'},
      'now': {'pt': 'agora', 'es': 'ahora', 'fr': 'maintenant', 'de': 'jetzt', 'it': 'ora', 'ja': '今', 'ko': '지금', 'zh': '现在'},
      'later': {'pt': 'mais tarde', 'es': 'más tarde', 'fr': 'plus tard', 'de': 'später', 'it': 'più tardi', 'ja': '後で', 'ko': '나중에', 'zh': '稍后'},

      // Numbers
      'one': {'pt': 'um', 'es': 'uno', 'fr': 'un', 'de': 'eins', 'it': 'uno', 'ja': '一', 'ko': '하나', 'zh': '一'},
      'two': {'pt': 'dois', 'es': 'dos', 'fr': 'deux', 'de': 'zwei', 'it': 'due', 'ja': '二', 'ko': '둘', 'zh': '二'},
      'three': {'pt': 'três', 'es': 'tres', 'fr': 'trois', 'de': 'drei', 'it': 'tre', 'ja': '三', 'ko': '셋', 'zh': '三'},

      // Food
      'water': {'pt': 'água', 'es': 'agua', 'fr': 'eau', 'de': 'wasser', 'it': 'acqua', 'ja': '水', 'ko': '물', 'zh': '水'},
      'food': {'pt': 'comida', 'es': 'comida', 'fr': 'nourriture', 'de': 'essen', 'it': 'cibo', 'ja': '食べ物', 'ko': '음식', 'zh': '食物'},
      'bread': {'pt': 'pão', 'es': 'pan', 'fr': 'pain', 'de': 'brot', 'it': 'pane', 'ja': 'パン', 'ko': '빵', 'zh': '面包'},

      // Colors
      'red': {'pt': 'vermelho', 'es': 'rojo', 'fr': 'rouge', 'de': 'rot', 'it': 'rosso', 'ja': '赤', 'ko': '빨간색', 'zh': '红色'},
      'blue': {'pt': 'azul', 'es': 'azul', 'fr': 'bleu', 'de': 'blau', 'it': 'blu', 'ja': '青', 'ko': '파란색', 'zh': '蓝色'},
      'green': {'pt': 'verde', 'es': 'verde', 'fr': 'vert', 'de': 'grün', 'it': 'verde', 'ja': '緑', 'ko': '초록색', 'zh': '绿色'},
      'white': {'pt': 'branco', 'es': 'blanco', 'fr': 'blanc', 'de': 'weiß', 'it': 'bianco', 'ja': '白', 'ko': '흰색', 'zh': '白色'},
      'black': {'pt': 'preto', 'es': 'negro', 'fr': 'noir', 'de': 'schwarz', 'it': 'nero', 'ja': '黒', 'ko': '검은색', 'zh': '黑色'},
    };
  }

  /// Preserve capitalization from original text
  String _preserveCapitalization(String original, String translation) {
    if (original.isEmpty || translation.isEmpty) return translation;

    if (original[0].toUpperCase() == original[0]) {
      return translation[0].toUpperCase() + translation.substring(1);
    }

    return translation;
  }

  /// Translate using patterns
  String? _translateByPattern(String text, String sourceLanguage, String targetLanguage) {
    final lowerText = text.toLowerCase();

    // "I love you" pattern
    if (lowerText.contains('i love you') || lowerText.contains('love you')) {
      switch (targetLanguage) {
        case 'pt': return 'Eu te amo';
        case 'es': return 'Te amo';
        case 'fr': return 'Je t\'aime';
        case 'de': return 'Ich liebe dich';
        case 'it': return 'Ti amo';
        case 'ja': return '愛してる';
        case 'ko': return '사랑해요';
        case 'zh': return '我爱你';
      }
    }

    // "How are you" pattern
    if (lowerText.contains('how are you')) {
      switch (targetLanguage) {
        case 'pt': return 'Como você está?';
        case 'es': return '¿Cómo estás?';
        case 'fr': return 'Comment allez-vous?';
        case 'de': return 'Wie geht es dir?';
        case 'it': return 'Come stai?';
        case 'ja': return '元気ですか？';
        case 'ko': return '어떻게 지내세요?';
        case 'zh': return '你好吗？';
      }
    }

    // "What is your name" pattern
    if (lowerText.contains('what is your name') || lowerText.contains('what\'s your name')) {
      switch (targetLanguage) {
        case 'pt': return 'Qual é o seu nome?';
        case 'es': return '¿Cómo te llamas?';
        case 'fr': return 'Comment vous appelez-vous?';
        case 'de': return 'Wie heißt du?';
        case 'it': return 'Come ti chiami?';
        case 'ja': return 'お名前は何ですか？';
        case 'ko': return '이름이 뭐예요?';
        case 'zh': return '你叫什么名字？';
      }
    }

    return null;
  }

  /// Get "no translation available" message in target language
  String _getNoTranslationMessage(String targetLanguage) {
    switch (targetLanguage) {
      case 'pt': return 'Tradução não disponível';
      case 'es': return 'Traducción no disponible';
      case 'fr': return 'Traduction non disponible';
      case 'de': return 'Übersetzung nicht verfügbar';
      case 'it': return 'Traduzione non disponibile';
      case 'ja': return '翻訳できません';
      case 'ko': return '번역할 수 없습니다';
      case 'zh': return '无法翻译';
      default: return 'Translation not available';
    }
  }

  /// Translate image using native Gemma-3N multimodal capabilities (placeholder)
  Future<TranslationResult> translateImage({
    required Uint8List imageBytes,
    required String targetLanguage,
    String? sourceLanguage,
    String? additionalContext,
  }) async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      if (kDebugMode) {
        print('🔄 Translating image with Flutter Gemma (placeholder)...');
        print('🖼️ Image size: ${imageBytes.length} bytes');
        print('🌍 Target language: $targetLanguage');
      }

      // Simulate processing time
      await Future.delayed(const Duration(seconds: 2));

      // Placeholder translation
      const translatedText = '[Flutter Gemma Placeholder] Image text translated';

      if (kDebugMode) {
        print('✅ Placeholder image translation completed!');
      }

      return TranslationResult(
        originalText: 'Text extracted from image',
        translatedText: translatedText,
        sourceLanguage: sourceLanguage ?? 'auto',
        targetLanguage: targetLanguage,
        confidence: 0.95,
        timestamp: DateTime.now(),
        processingTime: const Duration(seconds: 2),
        metadata: {
          'model': _currentModel,
          'service': 'flutter_gemma_placeholder',
          'platform': defaultTargetPlatform.name,
          'native': true,
          'multimodal': true,
          'placeholder': true,
          'image_size': imageBytes.length,
        },
      );
    } catch (e) {
      if (kDebugMode) {
        print('❌ Placeholder image translation failed: $e');
      }
      
      // Return fallback translation
      return TranslationResult(
        originalText: 'Image content',
        translatedText: 'Image translation error: $e',
        sourceLanguage: sourceLanguage ?? 'auto',
        targetLanguage: targetLanguage,
        confidence: 0.0,
        timestamp: DateTime.now(),
        metadata: {'error': e.toString(), 'type': 'image', 'service': 'flutter_gemma_placeholder'},
      );
    }
  }

  /// Check if service is healthy
  Future<bool> isHealthy() async {
    return _isInitialized;
  }

  /// Check if offline mode is available (always true for native)
  Future<bool> isOfflineAvailable() async {
    return _isInitialized;
  }

  /// Switch to offline mode (no-op since always offline)
  Future<void> switchToOfflineMode() async {
    if (kDebugMode) {
      print('🔒 Already in native offline mode');
    }
  }

  /// Switch to online mode (no-op since always offline)
  Future<void> switchToOnlineMode() async {
    if (kDebugMode) {
      print('🔒 Cannot switch to online mode - using native AI exclusively');
    }
  }

  /// Get current service status
  Map<String, dynamic> getStatus() {
    return {
      'initialized': _isInitialized,
      'offline_mode': true, // Always offline with native AI
      'current_model': _currentModel,
      'service_type': 'flutter_gemma_placeholder',
      'platform': defaultTargetPlatform.name,
      'model_loaded': _isInitialized,
      'multimodal_capable': true,
      'native': true,
      'placeholder': true,
    };
  }

  /// Dispose resources
  Future<void> dispose() async {
    try {
      _isInitialized = false;
      _currentModel = '';

      if (kDebugMode) {
        print('🗑️ Native Translation Service disposed');
      }
    } catch (e) {
      if (kDebugMode) {
        print('⚠️ Error disposing Native Translation Service: $e');
      }
    }
  }
}
