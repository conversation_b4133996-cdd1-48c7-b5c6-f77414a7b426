import 'dart:async';
import 'dart:convert';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';

import '../models/translation_result.dart';
import '../models/image_message.dart';
import '../config/api_keys.dart';
import 'ollama_translation_service.dart';

/// Real translation service using Ollama with Gemma-3N exclusively (100% offline)
class RealTranslationService {
  static final RealTranslationService _instance = RealTranslationService._internal();
  static RealTranslationService get instance => _instance;
  RealTranslationService._internal();

  OllamaTranslationService? _ollamaService;
  bool _isInitialized = false;
  String _currentModel = '';

  bool get isInitialized => _isInitialized;
  String get currentModel => _currentModel;
  bool get isOfflineMode => true; // Always offline with Ollama

  /// Initialize Ollama translation service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      if (kDebugMode) {
        print('🚀 Initializing Real Translation Service with Ollama...');
      }

      _ollamaService = OllamaTranslationService.instance;
      final success = await _ollamaService!.initialize();
      
      if (success) {
        _isInitialized = true;
        _currentModel = ApiKeys.ollamaModel;
        
        if (kDebugMode) {
          print('✅ Real Translation Service initialized successfully!');
          print('🧠 Using Ollama model: $_currentModel');
          print('🌐 Host: ${ApiKeys.ollamaHost}');
          print('🔒 Mode: 100% Offline');
        }
      } else {
        throw Exception('Failed to initialize Ollama service');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to initialize Real Translation Service: $e');
      }
      rethrow;
    }
  }

  /// Translate text using Ollama with Gemma-3N
  Future<TranslationResult> translateText({
    required String text,
    required String sourceLanguage,
    required String targetLanguage,
    String? context,
    String? domain,
  }) async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      return await _ollamaService!.translateText(
        text: text,
        sourceLanguage: sourceLanguage,
        targetLanguage: targetLanguage,
        context: context,
        domain: domain,
      );
    } catch (e) {
      if (kDebugMode) {
        print('❌ Translation failed: $e');
      }
      
      // Return fallback translation
      return TranslationResult(
        originalText: text,
        translatedText: 'Translation error: $e',
        sourceLanguage: sourceLanguage,
        targetLanguage: targetLanguage,
        confidence: 0.0,
        timestamp: DateTime.now(),
        metadata: {'error': e.toString()},
      );
    }
  }

  /// Translate image using Ollama with Gemma-3N multimodal capabilities
  Future<TranslationResult> translateImage({
    required Uint8List imageBytes,
    required String targetLanguage,
    String? sourceLanguage,
    String? additionalContext,
  }) async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      return await _ollamaService!.translateImage(
        imageBytes: imageBytes,
        targetLanguage: targetLanguage,
        sourceLanguage: sourceLanguage,
        context: additionalContext,
      );
    } catch (e) {
      if (kDebugMode) {
        print('❌ Image translation failed: $e');
      }
      
      // Return fallback translation
      return TranslationResult(
        originalText: 'Image content',
        translatedText: 'Image translation error: $e',
        sourceLanguage: sourceLanguage ?? 'auto',
        targetLanguage: targetLanguage,
        confidence: 0.0,
        timestamp: DateTime.now(),
        metadata: {'error': e.toString(), 'type': 'image'},
      );
    }
  }

  /// Check if service is healthy
  Future<bool> isHealthy() async {
    if (!_isInitialized) return false;
    return await _ollamaService?.isHealthy() ?? false;
  }

  /// Check if offline mode is available (always true for Ollama)
  Future<bool> isOfflineAvailable() async {
    try {
      if (_ollamaService == null) {
        _ollamaService = OllamaTranslationService.instance;
      }
      return await _ollamaService!.isHealthy();
    } catch (e) {
      return false;
    }
  }

  /// Switch to offline mode (no-op since always offline)
  Future<void> switchToOfflineMode() async {
    if (kDebugMode) {
      print('🔒 Already in offline mode with Ollama');
    }
  }

  /// Switch to online mode (no-op since always offline)
  Future<void> switchToOnlineMode() async {
    if (kDebugMode) {
      print('🔒 Cannot switch to online mode - using Ollama exclusively');
    }
  }

  /// Get current service status
  Map<String, dynamic> getStatus() {
    return {
      'initialized': _isInitialized,
      'offline_mode': true, // Always offline with Ollama
      'current_model': _currentModel,
      'service_type': 'ollama_gemma_3n',
      'host': ApiKeys.ollamaHost,
    };
  }

  /// Dispose resources
  void dispose() {
    _ollamaService?.dispose();
    _ollamaService = null;
    _isInitialized = false;
    _currentModel = '';
  }

  /// Create image message for compatibility
  ImageMessage createImageMessage({
    required Uint8List imageBytes,
    String? text,
  }) {
    return ImageMessage(
      imageBytes: imageBytes,
      text: text ?? '',
      timestamp: DateTime.now(),
    );
  }

  /// Extract translation from response (utility method)
  String extractTranslation(String response) {
    // Remove common prefixes and suffixes
    String cleaned = response.trim();
    
    // Remove translation markers
    cleaned = cleaned.replaceAll(RegExp(r'^(Translation:|Tradução:|Translated text:|Texto traduzido:)\s*', caseSensitive: false), '');
    cleaned = cleaned.replaceAll(RegExp(r'^(Here is the translation:|Aqui está a tradução:)\s*', caseSensitive: false), '');
    
    // Remove quotes if the entire response is quoted
    if (cleaned.startsWith('"') && cleaned.endsWith('"')) {
      cleaned = cleaned.substring(1, cleaned.length - 1);
    }
    
    return cleaned.trim();
  }

  /// Calculate confidence score (utility method)
  double calculateConfidence(String response) {
    // Basic confidence calculation for Ollama responses
    double confidence = 0.8; // Base confidence
    
    // Increase confidence for longer, more detailed responses
    if (response.length > 50) confidence += 0.1;
    if (response.length > 100) confidence += 0.05;
    
    // Decrease confidence for very short responses
    if (response.length < 10) confidence -= 0.3;
    
    return confidence.clamp(0.0, 1.0);
  }

  /// Build translation prompt (utility method)
  String buildTranslationPrompt({
    required String text,
    required String sourceLanguage,
    required String targetLanguage,
    String? context,
    String? domain,
  }) {
    final buffer = StringBuffer();
    
    buffer.writeln('Translate the following text from $sourceLanguage to $targetLanguage:');
    buffer.writeln();
    
    if (context != null && context.isNotEmpty) {
      buffer.writeln('Context: $context');
      buffer.writeln();
    }
    
    if (domain != null && domain.isNotEmpty) {
      buffer.writeln('Domain: $domain');
      buffer.writeln();
    }
    
    buffer.writeln('Text to translate:');
    buffer.writeln(text);
    buffer.writeln();
    buffer.writeln('Translation:');
    
    return buffer.toString();
  }

  /// Get available models from Ollama
  Future<List<String>> getAvailableModels() async {
    if (_ollamaService == null) {
      _ollamaService = OllamaTranslationService.instance;
    }
    
    try {
      // This would need to be implemented in OllamaService
      return [_currentModel]; // For now, return current model
    } catch (e) {
      return [];
    }
  }
}
