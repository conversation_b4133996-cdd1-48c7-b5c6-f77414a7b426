import 'dart:async';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';

import '../models/translation_result.dart';
import 'ollama_service.dart';

/// Translation service using Ollama with Gemma-3N model
class OllamaTranslationService {
  static final OllamaTranslationService _instance = OllamaTranslationService._internal();
  static OllamaTranslationService get instance => _instance;
  OllamaTranslationService._internal();

  bool _isInitialized = false;
  final OllamaService _ollamaService = OllamaService.instance;

  bool get isInitialized => _isInitialized;

  /// Initialize the Ollama translation service
  Future<bool> initialize() async {
    if (_isInitialized) return true;

    try {
      if (kDebugMode) {
        print('🚀 Initializing Ollama Translation Service...');
      }

      // Initialize Ollama service
      final success = await _ollamaService.initialize();
      
      if (success) {
        _isInitialized = true;
        if (kDebugMode) {
          print('✅ Ollama Translation Service initialized successfully!');
          print('🧠 Using model: ${_ollamaService.currentModel}');
          print('🌐 Ollama host: ${_ollamaService.ollamaHost}');
        }
        return true;
      } else {
        if (kDebugMode) {
          print('❌ Failed to initialize Ollama service');
        }
        return false;
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to initialize Ollama Translation Service: $e');
      }
      return false;
    }
  }

  /// Translate text using Ollama with Gemma-3N
  Future<TranslationResult> translateText({
    required String text,
    required String sourceLanguage,
    required String targetLanguage,
    String? context,
    String? domain,
  }) async {
    if (!_isInitialized) {
      final success = await initialize();
      if (!success) {
        throw Exception('Ollama Translation Service not initialized');
      }
    }

    try {
      final startTime = DateTime.now();
      
      if (kDebugMode) {
        print('🔄 Translating text with Ollama...');
        print('📝 Text: ${text.length > 50 ? '${text.substring(0, 50)}...' : text}');
        print('🌍 From: $sourceLanguage → To: $targetLanguage');
      }

      // Build optimized prompt for Gemma-3N
      final prompt = _buildTranslationPrompt(
        text: text,
        sourceLanguage: sourceLanguage,
        targetLanguage: targetLanguage,
        context: context,
        domain: domain,
      );

      // Generate translation using Ollama
      final translatedText = await _ollamaService.generateText(
        prompt: prompt,
        systemPrompt: "You are a professional translator. Provide only the translation without explanations or additional text.",
      );

      // Clean up the response
      final cleanedTranslation = _cleanTranslationResponse(translatedText);
      
      final processingTime = DateTime.now().difference(startTime);
      
      if (kDebugMode) {
        print('✅ Translation completed with Ollama!');
        print('⏱️ Processing time: ${processingTime.inMilliseconds}ms');
        print('🎯 Result: ${cleanedTranslation.length > 50 ? '${cleanedTranslation.substring(0, 50)}...' : cleanedTranslation}');
      }

      return TranslationResult(
        originalText: text,
        translatedText: cleanedTranslation,
        sourceLanguage: sourceLanguage,
        targetLanguage: targetLanguage,
        confidence: 0.95, // Gemma-3N generally has high confidence
        timestamp: DateTime.now(),
        processingTime: processingTime,
        metadata: {
          'model': _ollamaService.currentModel,
          'service': 'ollama',
          'host': _ollamaService.ollamaHost,
          'offline': true,
          'architecture': 'MatFormer',
          'api': 'ollama_local',
        },
      );
    } catch (e) {
      if (kDebugMode) {
        print('❌ Ollama translation failed: $e');
      }
      rethrow;
    }
  }

  /// Translate image using Ollama with Gemma-3N multimodal capabilities
  Future<TranslationResult> translateImage({
    required Uint8List imageBytes,
    required String targetLanguage,
    String? sourceLanguage,
    String? context,
  }) async {
    if (!_isInitialized) {
      final success = await initialize();
      if (!success) {
        throw Exception('Ollama Translation Service not initialized');
      }
    }

    try {
      final startTime = DateTime.now();
      
      if (kDebugMode) {
        print('🔄 Translating image with Ollama...');
        print('🖼️ Image size: ${imageBytes.length} bytes');
        print('🌍 Target language: $targetLanguage');
      }

      // Build optimized prompt for image translation
      final prompt = _buildImageTranslationPrompt(
        targetLanguage: targetLanguage,
        sourceLanguage: sourceLanguage,
        context: context,
      );

      // Generate translation using Ollama with image
      final response = await _ollamaService.generateTextWithImage(
        prompt: prompt,
        imageBytes: imageBytes,
        systemPrompt: "You are a professional translator. Extract text from the image and translate it. Provide only the translated text without explanations.",
      );

      // Parse the response to extract translation
      final translationData = _parseImageTranslationResponse(response);
      
      final processingTime = DateTime.now().difference(startTime);
      
      if (kDebugMode) {
        print('✅ Image translation completed with Ollama!');
        print('⏱️ Processing time: ${processingTime.inMilliseconds}ms');
      }

      return TranslationResult(
        originalText: translationData['extracted_text'] ?? 'Text extracted from image',
        translatedText: translationData['translated_text'] ?? response,
        sourceLanguage: translationData['detected_language'] ?? sourceLanguage ?? 'auto',
        targetLanguage: targetLanguage,
        confidence: 0.95,
        timestamp: DateTime.now(),
        processingTime: processingTime,
        metadata: {
          'model': _ollamaService.currentModel,
          'service': 'ollama',
          'host': _ollamaService.ollamaHost,
          'offline': true,
          'architecture': 'MatFormer',
          'api': 'ollama_local',
          'multimodal': true,
          'extracted_text': translationData['extracted_text'],
          'detected_language': translationData['detected_language'],
        },
      );
    } catch (e) {
      if (kDebugMode) {
        print('❌ Ollama image translation failed: $e');
      }
      rethrow;
    }
  }

  /// Build translation prompt optimized for Gemma-3N
  String _buildTranslationPrompt({
    required String text,
    required String sourceLanguage,
    required String targetLanguage,
    String? context,
    String? domain,
  }) {
    final buffer = StringBuffer();
    
    buffer.writeln('Translate the following text from $sourceLanguage to $targetLanguage:');
    buffer.writeln();
    
    if (context != null && context.isNotEmpty) {
      buffer.writeln('Context: $context');
      buffer.writeln();
    }
    
    if (domain != null && domain.isNotEmpty) {
      buffer.writeln('Domain: $domain');
      buffer.writeln();
    }
    
    buffer.writeln('Text to translate:');
    buffer.writeln(text);
    buffer.writeln();
    buffer.writeln('Translation:');
    
    return buffer.toString();
  }

  /// Build image translation prompt optimized for Gemma-3N
  String _buildImageTranslationPrompt({
    required String targetLanguage,
    String? sourceLanguage,
    String? context,
  }) {
    final buffer = StringBuffer();
    
    buffer.writeln('Extract all text from this image and translate it to $targetLanguage.');
    
    if (sourceLanguage != null && sourceLanguage != 'auto') {
      buffer.writeln('The source language is $sourceLanguage.');
    }
    
    if (context != null && context.isNotEmpty) {
      buffer.writeln('Context: $context');
    }
    
    buffer.writeln();
    buffer.writeln('Provide only the translated text, without any explanations or additional information.');
    
    return buffer.toString();
  }

  /// Clean translation response from Gemma-3N
  String _cleanTranslationResponse(String response) {
    // Remove common prefixes and suffixes
    String cleaned = response.trim();
    
    // Remove translation markers
    cleaned = cleaned.replaceAll(RegExp(r'^(Translation:|Tradução:|Translated text:|Texto traduzido:)\s*', caseSensitive: false), '');
    cleaned = cleaned.replaceAll(RegExp(r'^(Here is the translation:|Aqui está a tradução:)\s*', caseSensitive: false), '');
    
    // Remove quotes if the entire response is quoted
    if (cleaned.startsWith('"') && cleaned.endsWith('"')) {
      cleaned = cleaned.substring(1, cleaned.length - 1);
    }
    
    return cleaned.trim();
  }

  /// Parse image translation response
  Map<String, String> _parseImageTranslationResponse(String response) {
    // Try to extract structured information if available
    final result = <String, String>{};
    
    // For now, treat the entire response as translated text
    // In the future, we could implement more sophisticated parsing
    result['translated_text'] = _cleanTranslationResponse(response);
    
    return result;
  }

  /// Check if service is healthy
  Future<bool> isHealthy() async {
    return _ollamaService.isHealthy();
  }

  /// Dispose resources
  void dispose() {
    _isInitialized = false;
  }
}
