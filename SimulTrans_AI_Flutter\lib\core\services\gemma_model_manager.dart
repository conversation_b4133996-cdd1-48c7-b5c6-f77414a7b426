import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter_gemma/flutter_gemma.dart';

/// Gemma Model Manager for downloading and managing Gemma-3N models
/// 
/// This service handles model downloads, installation, and management
/// for native Flutter Gemma with progress tracking and error handling.
class GemmaModelManager {
  static final GemmaModelManager _instance = GemmaModelManager._internal();
  static GemmaModelManager get instance => _instance;
  GemmaModelManager._internal();

  ModelFileManager? _modelManager;
  bool _isInitialized = false;
  String? _currentModelPath;
  String? _currentLoraPath;

  bool get isInitialized => _isInitialized;
  String? get currentModelPath => _currentModelPath;
  String? get currentLoraPath => _currentLoraPath;

  /// Recommended Gemma-3N models with their URLs
  static const Map<String, Map<String, String>> recommendedModels = {
    'gemma3n_e2b': {
      'name': 'Gemma-3N E2B (Recommended)',
      'description': '1.5B parameters with 2B effective, multimodal support',
      'url': 'https://huggingface.co/google/gemma-3n-E2B-it-litert-preview/resolve/main/model.bin',
      'size': '~1.8GB',
      'type': 'multimodal',
    },
    'gemma3n_e4b': {
      'name': 'Gemma-3N E4B (High Quality)',
      'description': '1.5B parameters with 4B effective, multimodal support',
      'url': 'https://huggingface.co/google/gemma-3n-E4B-it-litert-preview/resolve/main/model.bin',
      'size': '~2.2GB',
      'type': 'multimodal',
    },
    'gemma3_1b': {
      'name': 'Gemma-3 1B (Lightweight)',
      'description': '1B parameters, text-only, fastest performance',
      'url': 'https://huggingface.co/litert-community/Gemma3-1B-IT/resolve/main/model.bin',
      'size': '~1.2GB',
      'type': 'text_only',
    },
  };

  /// Initialize model manager
  Future<bool> initialize() async {
    if (_isInitialized) return true;

    try {
      if (kDebugMode) {
        print('🚀 Initializing Gemma Model Manager...');
      }

      final gemmaPlugin = FlutterGemmaPlugin.instance;
      _modelManager = gemmaPlugin.modelManager;

      _isInitialized = true;

      if (kDebugMode) {
        print('✅ Gemma Model Manager initialized successfully!');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to initialize Gemma Model Manager: $e');
      }
      return false;
    }
  }

  /// Download model from network with progress tracking
  Future<bool> downloadModel({
    required String modelKey,
    String? loraUrl,
    Function(double)? onProgress,
    VoidCallback? onComplete,
    Function(String)? onError,
  }) async {
    if (!_isInitialized) {
      final success = await initialize();
      if (!success) return false;
    }

    if (!recommendedModels.containsKey(modelKey)) {
      final error = 'Unknown model key: $modelKey';
      if (kDebugMode) print('❌ $error');
      onError?.call(error);
      return false;
    }

    final modelInfo = recommendedModels[modelKey]!;
    final modelUrl = modelInfo['url']!;

    try {
      if (kDebugMode) {
        print('🔄 Downloading ${modelInfo['name']}...');
        print('📦 Size: ${modelInfo['size']}');
        print('🌐 URL: $modelUrl');
      }

      // Create progress stream
      final progressController = StreamController<double>();
      
      // Listen to progress
      progressController.stream.listen(
        (progress) {
          if (kDebugMode) {
            print('📥 Download progress: ${(progress * 100).toStringAsFixed(1)}%');
          }
          onProgress?.call(progress);
        },
        onDone: () {
          if (kDebugMode) {
            print('✅ Model download completed!');
          }
          onComplete?.call();
        },
        onError: (error) {
          if (kDebugMode) {
            print('❌ Download error: $error');
          }
          onError?.call(error.toString());
        },
      );

      // Start download with progress tracking
      _modelManager!.downloadModelFromNetworkWithProgress(
        modelUrl,
        loraUrl: loraUrl,
      ).listen(
        (progress) => progressController.add(progress / 100.0),
        onDone: () => progressController.close(),
        onError: (error) => progressController.addError(error),
      );

      // Wait for completion
      await progressController.stream.last;

      _currentModelPath = modelUrl.split('/').last;
      if (loraUrl != null) {
        _currentLoraPath = loraUrl.split('/').last;
      }

      return true;
    } catch (e) {
      final error = 'Failed to download model: $e';
      if (kDebugMode) print('❌ $error');
      onError?.call(error);
      return false;
    }
  }

  /// Install model from assets (debug mode only)
  Future<bool> installModelFromAssets({
    required String modelAssetPath,
    String? loraAssetPath,
    Function(double)? onProgress,
    VoidCallback? onComplete,
    Function(String)? onError,
  }) async {
    if (!_isInitialized) {
      final success = await initialize();
      if (!success) return false;
    }

    if (!kDebugMode) {
      final error = 'Asset installation only available in debug mode';
      if (kDebugMode) print('❌ $error');
      onError?.call(error);
      return false;
    }

    try {
      if (kDebugMode) {
        print('🔄 Installing model from assets...');
        print('📁 Model: $modelAssetPath');
        if (loraAssetPath != null) {
          print('🎯 LoRA: $loraAssetPath');
        }
      }

      // Create progress stream for asset installation
      final progressController = StreamController<double>();
      
      progressController.stream.listen(
        (progress) {
          if (kDebugMode) {
            print('📥 Installation progress: ${(progress * 100).toStringAsFixed(1)}%');
          }
          onProgress?.call(progress);
        },
        onDone: () {
          if (kDebugMode) {
            print('✅ Model installation completed!');
          }
          onComplete?.call();
        },
        onError: (error) {
          if (kDebugMode) {
            print('❌ Installation error: $error');
          }
          onError?.call(error.toString());
        },
      );

      // Install from assets with progress
      _modelManager!.installModelFromAssetWithProgress(
        modelAssetPath,
        loraPath: loraAssetPath,
      ).listen(
        (progress) => progressController.add(progress / 100.0),
        onDone: () => progressController.close(),
        onError: (error) => progressController.addError(error),
      );

      // Wait for completion
      await progressController.stream.last;

      _currentModelPath = modelAssetPath;
      _currentLoraPath = loraAssetPath;

      return true;
    } catch (e) {
      final error = 'Failed to install model from assets: $e';
      if (kDebugMode) print('❌ $error');
      onError?.call(error);
      return false;
    }
  }

  /// Check if a model is already installed
  Future<bool> isModelInstalled() async {
    if (!_isInitialized) return false;

    try {
      // This is a simplified check - in a real implementation,
      // you would check the actual model files on disk
      return _currentModelPath != null;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error checking model installation: $e');
      }
      return false;
    }
  }

  /// Delete installed model
  Future<bool> deleteModel() async {
    if (!_isInitialized) return false;

    try {
      if (kDebugMode) {
        print('🗑️ Deleting installed model...');
      }

      await _modelManager!.deleteModel();
      _currentModelPath = null;

      if (kDebugMode) {
        print('✅ Model deleted successfully');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to delete model: $e');
      }
      return false;
    }
  }

  /// Delete LoRA weights
  Future<bool> deleteLoraWeights() async {
    if (!_isInitialized) return false;

    try {
      if (kDebugMode) {
        print('🗑️ Deleting LoRA weights...');
      }

      await _modelManager!.deleteLoraWeights();
      _currentLoraPath = null;

      if (kDebugMode) {
        print('✅ LoRA weights deleted successfully');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to delete LoRA weights: $e');
      }
      return false;
    }
  }

  /// Get recommended model for translation
  String getRecommendedModelKey() {
    // For translation, we recommend Gemma-3N E2B for best balance of speed and quality
    return 'gemma3n_e2b';
  }

  /// Get model information
  Map<String, dynamic>? getModelInfo(String modelKey) {
    return recommendedModels[modelKey];
  }

  /// Get all available models
  Map<String, Map<String, String>> getAllModels() {
    return Map.from(recommendedModels);
  }

  /// Get model status
  Map<String, dynamic> getModelStatus() {
    return {
      'initialized': _isInitialized,
      'model_installed': _currentModelPath != null,
      'lora_installed': _currentLoraPath != null,
      'current_model': _currentModelPath,
      'current_lora': _currentLoraPath,
    };
  }

  /// Set model path manually
  Future<void> setModelPath(String path) async {
    if (!_isInitialized) return;

    try {
      await _modelManager!.setModelPath(path);
      _currentModelPath = path;

      if (kDebugMode) {
        print('📁 Model path set to: $path');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to set model path: $e');
      }
    }
  }

  /// Set LoRA weights path manually
  Future<void> setLoraWeightsPath(String path) async {
    if (!_isInitialized) return;

    try {
      await _modelManager!.setLoraWeightsPath(path);
      _currentLoraPath = path;

      if (kDebugMode) {
        print('🎯 LoRA path set to: $path');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to set LoRA path: $e');
      }
    }
  }

  /// Dispose resources
  void dispose() {
    _isInitialized = false;
    _currentModelPath = null;
    _currentLoraPath = null;

    if (kDebugMode) {
      print('🗑️ Gemma Model Manager disposed');
    }
  }
}
