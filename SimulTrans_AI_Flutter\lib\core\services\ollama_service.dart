import 'dart:async';
import 'dart:convert';
import 'dart:typed_data';
import 'package:http/http.dart' as http;
import 'package:flutter/foundation.dart';

import '../models/translation_result.dart';
import '../config/api_keys.dart';

/// Ollama service for communicating with local Gemma-3N model
class OllamaService {
  static final OllamaService _instance = OllamaService._internal();
  static OllamaService get instance => _instance;
  OllamaService._internal();

  bool _isInitialized = false;
  bool _isConnected = false;
  String _currentModel = '';
  String _ollamaHost = '';
  int _connectionTimeout = 30;
  int _requestTimeout = 600;
  int _imageTimeout = 900;

  bool get isInitialized => _isInitialized;
  bool get isConnected => _isConnected;
  String get currentModel => _currentModel;
  String get ollamaHost => _ollamaHost;

  /// Initialize Ollama service with configuration from environment
  Future<bool> initialize() async {
    if (_isInitialized) return true;

    try {
      // Load configuration from environment
      _ollamaHost = ApiKeys.ollamaHost;
      _currentModel = ApiKeys.ollamaModel;
      _connectionTimeout = ApiKeys.ollamaConnectionTimeout;
      _requestTimeout = ApiKeys.ollamaTimeout;
      _imageTimeout = ApiKeys.ollamaImageTimeout;

      if (kDebugMode) {
        print('🚀 Initializing Ollama Service...');
        print('🌐 Host: $_ollamaHost');
        print('🧠 Model: $_currentModel');
        print('⏱️ Timeouts: Connection=${_connectionTimeout}s, Request=${_requestTimeout}s, Image=${_imageTimeout}s');
      }

      // Test connection to Ollama
      _isConnected = await _testConnection();
      
      if (_isConnected) {
        // Verify model is available
        final availableModels = await _listAvailableModels();
        if (availableModels.contains(_currentModel)) {
          _isInitialized = true;
          if (kDebugMode) {
            print('✅ Ollama Service initialized successfully!');
            print('🎯 Model $_currentModel is available and ready');
          }
          return true;
        } else {
          if (kDebugMode) {
            print('❌ Model $_currentModel not found in Ollama');
            print('📋 Available models: $availableModels');
            print('💡 Run: ollama pull $_currentModel');
          }
          return false;
        }
      } else {
        if (kDebugMode) {
          print('❌ Cannot connect to Ollama at $_ollamaHost');
          print('💡 Make sure Ollama is running: ollama serve');
        }
        return false;
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to initialize Ollama Service: $e');
      }
      return false;
    }
  }

  /// Test connection to Ollama server
  Future<bool> _testConnection() async {
    try {
      final response = await http.get(
        Uri.parse('$_ollamaHost/api/tags'),
        headers: {'Content-Type': 'application/json'},
      ).timeout(Duration(seconds: _connectionTimeout));

      return response.statusCode == 200;
    } catch (e) {
      if (kDebugMode) {
        print('Connection test failed: $e');
      }
      return false;
    }
  }

  /// List available models in Ollama
  Future<List<String>> _listAvailableModels() async {
    try {
      final response = await http.get(
        Uri.parse('$_ollamaHost/api/tags'),
        headers: {'Content-Type': 'application/json'},
      ).timeout(Duration(seconds: _connectionTimeout));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final models = data['models'] as List<dynamic>? ?? [];
        return models.map((model) => model['name'] as String).toList();
      }
      return [];
    } catch (e) {
      if (kDebugMode) {
        print('Failed to list models: $e');
      }
      return [];
    }
  }

  /// Generate text using Ollama API
  Future<String> generateText({
    required String prompt,
    String? systemPrompt,
    double? temperature,
    int? maxTokens,
  }) async {
    if (!_isInitialized || !_isConnected) {
      throw Exception('Ollama service not initialized or connected');
    }

    try {
      final messages = <Map<String, String>>[];
      
      if (systemPrompt != null && systemPrompt.isNotEmpty) {
        messages.add({
          'role': 'system',
          'content': systemPrompt,
        });
      }
      
      messages.add({
        'role': 'user',
        'content': prompt,
      });

      final payload = {
        'model': _currentModel,
        'messages': messages,
        'stream': false,
        'options': {
          'temperature': temperature ?? ApiKeys.temperature,
          'num_predict': maxTokens ?? ApiKeys.maxTokens,
          'top_p': ApiKeys.topP,
        },
      };

      final response = await http.post(
        Uri.parse('$_ollamaHost/api/chat'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode(payload),
      ).timeout(Duration(seconds: _requestTimeout));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final message = data['message'];
        if (message != null && message['content'] != null) {
          return message['content'] as String;
        } else {
          throw Exception('Invalid response format from Ollama');
        }
      } else {
        throw Exception('Ollama API error: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Text generation failed: $e');
      }
      rethrow;
    }
  }

  /// Generate text with image using Ollama API (multimodal)
  Future<String> generateTextWithImage({
    required String prompt,
    required Uint8List imageBytes,
    String? systemPrompt,
    double? temperature,
    int? maxTokens,
  }) async {
    if (!_isInitialized || !_isConnected) {
      throw Exception('Ollama service not initialized or connected');
    }

    try {
      final messages = <Map<String, dynamic>>[];
      
      if (systemPrompt != null && systemPrompt.isNotEmpty) {
        messages.add({
          'role': 'system',
          'content': systemPrompt,
        });
      }
      
      // Convert image to base64
      final base64Image = base64Encode(imageBytes);
      
      messages.add({
        'role': 'user',
        'content': prompt,
        'images': [base64Image],
      });

      final payload = {
        'model': _currentModel,
        'messages': messages,
        'stream': false,
        'options': {
          'temperature': temperature ?? ApiKeys.temperature,
          'num_predict': maxTokens ?? ApiKeys.maxTokens,
          'top_p': ApiKeys.topP,
        },
      };

      final response = await http.post(
        Uri.parse('$_ollamaHost/api/chat'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode(payload),
      ).timeout(Duration(seconds: _imageTimeout));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final message = data['message'];
        if (message != null && message['content'] != null) {
          return message['content'] as String;
        } else {
          throw Exception('Invalid response format from Ollama');
        }
      } else {
        throw Exception('Ollama API error: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Image generation failed: $e');
      }
      rethrow;
    }
  }

  /// Check if Ollama service is healthy
  Future<bool> isHealthy() async {
    try {
      return await _testConnection();
    } catch (e) {
      return false;
    }
  }

  /// Get available models
  Future<List<String>> getAvailableModels() async {
    return await _listAvailableModels();
  }

  /// Dispose resources
  void dispose() {
    _isInitialized = false;
    _isConnected = false;
  }
}
