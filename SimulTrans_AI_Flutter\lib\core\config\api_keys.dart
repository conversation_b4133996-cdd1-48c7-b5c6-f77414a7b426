import 'package:flutter_dotenv/flutter_dotenv.dart';

/// API Keys configuration using environment variables
///
/// IMPORTANT: API keys are loaded from .env file
/// Never commit real API keys to version control!
///
/// To get your Google AI API key:
/// 1. Go to https://makersuite.google.com/app/apikey
/// 2. Create a new API key
/// 3. Add it to .env file as GEMINI_API_KEY=your_key_here
///
class ApiKeys {
  /// Initialize environment variables
  static Future<void> initialize() async {
    try {
      await dotenv.load(fileName: ".env");
    } catch (e) {
      print('Warning: Could not load .env file: $e');
    }
  }

  /// Google AI API Key for Gemini models from environment
  static String get googleAI => dotenv.env['GEMINI_API_KEY'] ?? '';

  /// Check if Google AI API key is configured
  static bool get isGoogleAIConfigured =>
      googleAI.isNotEmpty && googleAI != 'YOUR_GOOGLE_AI_API_KEY_HERE';

  /// Get Gemini model name from environment - Using best available model
  static String get geminiModel => dotenv.env['GEMINI_MODEL_NAME'] ?? 'gemini-2.5-flash';

  /// Get temperature setting from environment
  static double get temperature =>
      double.tryParse(dotenv.env['TEMPERATURE'] ?? '0.1') ?? 0.1;

  /// Get max tokens from environment
  static int get maxTokens =>
      int.tryParse(dotenv.env['MAX_NEW_TOKENS'] ?? '2048') ?? 2048;

  /// Get debug mode setting
  static bool get debugMode =>
      dotenv.env['DEBUG_MODE']?.toLowerCase() == 'true';

  /// Get app name from environment
  static String get appName => dotenv.env['APP_NAME'] ?? 'SimulTrans AI';

  /// Get app version from environment
  static String get appVersion => dotenv.env['APP_VERSION'] ?? '1.0.0';

  // ========== OLLAMA CONFIGURATION ==========

  /// Get Ollama host from environment
  static String get ollamaHost => dotenv.env['OLLAMA_HOST'] ?? 'http://localhost:11434';

  /// Get Ollama model name from environment
  static String get ollamaModel => dotenv.env['OLLAMA_MODEL_NAME'] ?? 'gemma3n:e2b';

  /// Get Ollama connection timeout from environment
  static int get ollamaConnectionTimeout =>
      int.tryParse(dotenv.env['OLLAMA_CONNECTION_TIMEOUT'] ?? '30') ?? 30;

  /// Get Ollama request timeout from environment
  static int get ollamaTimeout =>
      int.tryParse(dotenv.env['OLLAMA_TIMEOUT_SECONDS'] ?? '600') ?? 600;

  /// Get Ollama image timeout from environment
  static int get ollamaImageTimeout =>
      int.tryParse(dotenv.env['OLLAMA_IMAGE_TIMEOUT'] ?? '900') ?? 900;

  /// Get top_p setting from environment
  static double get topP =>
      double.tryParse(dotenv.env['TOP_P'] ?? '0.95') ?? 0.95;

  /// Check if Ollama is configured
  static bool get isOllamaConfigured =>
      ollamaHost.isNotEmpty && ollamaModel.isNotEmpty;
}

/// Instructions for getting API keys and configuring services
class ApiKeyInstructions {
  static const String ollama = '''
🚀 Para configurar o Ollama com Gemma-3N (RECOMENDADO - 100% OFFLINE):

1. 📥 Instale o Ollama: https://ollama.ai/download
2. 🔧 Inicie o servidor: ollama serve
3. 📦 Baixe o modelo Gemma-3N:
   ollama pull gemma3n:e2b  (modelo menor, mais rápido)
   ou
   ollama pull gemma3n:e4b  (modelo maior, mais preciso)

4. 📁 Configure no arquivo .env:
OLLAMA_HOST=http://localhost:11434
OLLAMA_MODEL_NAME=gemma3n:e2b
OLLAMA_CONNECTION_TIMEOUT=30
OLLAMA_TIMEOUT_SECONDS=600
OLLAMA_IMAGE_TIMEOUT=900

🎯 Modelos Gemma-3N suportados:
- gemma3n:e2b (2B parâmetros efetivos - rápido)
- gemma3n:e4b (4B parâmetros efetivos - preciso)

🚀 Vantagens do Ollama + Gemma-3N:
- 100% offline e privado
- Suporte multimodal (texto + imagem)
- Arquitetura MatFormer otimizada
- Sem limites de API ou custos
- Processamento local seguro

⚙️ Configurações de performance:
TEMPERATURE=0.1
MAX_NEW_TOKENS=2048
TOP_P=0.95

✅ Verificação:
- Ollama rodando: curl http://localhost:11434/api/tags
- Modelo disponível: ollama list
''';

  static const String googleAI = '''
🔑 Para configurar sua chave da API do Google AI (FALLBACK):

1. 🌐 Acesse: https://makersuite.google.com/app/apikey
2. 👤 Faça login com sua conta Google
3. ➕ Clique em "Create API Key"
4. 📋 Copie a chave gerada
5. 📁 Abra o arquivo .env na raiz do projeto
6. ✏️ Substitua YOUR_GOOGLE_AI_API_KEY_HERE pela sua chave real

Exemplo no arquivo .env:
GEMINI_API_KEY=AIzaSyD...sua_chave_aqui

🎯 Modelos Gemini suportados:
- gemini-2.5-flash (padrão - melhor custo-benefício)
- gemini-2.5-pro (mais poderoso para tarefas complexas)
- gemini-2.0-flash (nova geração com ferramentas)
- gemini-1.5-flash (rápido e versátil)
- gemini-1.5-pro (raciocínio complexo)

⚠️ IMPORTANTE:
- Nunca compartilhe sua chave da API publicamente!
- O arquivo .env deve estar no .gitignore
- Reinicie o app após alterar o .env
''';
}
