import 'dart:async';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:flutter_gemma/flutter_gemma.dart';

import '../models/translation_result.dart';
import '../config/api_keys.dart';

/// Native Flutter Gemma service for on-device AI with multimodal support
/// 
/// This service uses flutter_gemma plugin for native performance on Android/iOS
/// with support for Gemma-3N E2B/E4B models including vision capabilities.
class FlutterGemmaService {
  static final FlutterGemmaService _instance = FlutterGemmaService._internal();
  static FlutterGemmaService get instance => _instance;
  FlutterGemmaService._internal();

  FlutterGemmaPlugin? _gemmaPlugin;
  ModelFileManager? _modelManager;
  InferenceModel? _inferenceModel;
  bool _isInitialized = false;
  bool _isModelLoaded = false;
  String _currentModelType = '';
  PreferredBackend _preferredBackend = PreferredBackend.gpu;

  bool get isInitialized => _isInitialized;
  bool get isModelLoaded => _isModelLoaded;
  String get currentModelType => _currentModelType;
  PreferredBackend get preferredBackend => _preferredBackend;

  /// Initialize Flutter Gemma service
  Future<bool> initialize() async {
    if (_isInitialized) return true;

    try {
      if (kDebugMode) {
        print('🚀 Initializing Flutter Gemma Service...');
      }

      // Initialize Flutter Gemma plugin
      _gemmaPlugin = FlutterGemmaPlugin.instance;
      _modelManager = _gemmaPlugin!.modelManager;

      _isInitialized = true;

      if (kDebugMode) {
        print('✅ Flutter Gemma Service initialized successfully!');
        print('🧠 Ready for native on-device AI');
        print('📱 Platform: ${defaultTargetPlatform.name}');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to initialize Flutter Gemma Service: $e');
      }
      return false;
    }
  }

  /// Load Gemma-3N model for translation
  Future<bool> loadModel({
    ModelType modelType = ModelType.gemmaIt,
    PreferredBackend backend = PreferredBackend.gpu,
    bool supportImage = true,
    int maxTokens = 4096,
    int maxNumImages = 1,
  }) async {
    if (!_isInitialized) {
      final success = await initialize();
      if (!success) return false;
    }

    try {
      if (kDebugMode) {
        print('🔄 Loading Gemma-3N model...');
        print('🧠 Model type: ${modelType.name}');
        print('⚡ Backend: ${backend.name}');
        print('🖼️ Image support: $supportImage');
        print('📊 Max tokens: $maxTokens');
      }

      // Create inference model with multimodal support
      _inferenceModel = await _gemmaPlugin!.createModel(
        modelType: modelType,
        preferredBackend: backend,
        maxTokens: maxTokens,
        supportImage: supportImage,
        maxNumImages: maxNumImages,
      );

      _currentModelType = modelType.name;
      _preferredBackend = backend;
      _isModelLoaded = true;

      if (kDebugMode) {
        print('✅ Gemma-3N model loaded successfully!');
        print('🎯 Model: ${modelType.name}');
        print('🚀 Backend: ${backend.name}');
        print('🖼️ Multimodal: $supportImage');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to load Gemma-3N model: $e');
        print('💡 Make sure the model is downloaded and available');
      }
      return false;
    }
  }

  /// Translate text using native Gemma-3N
  Future<TranslationResult> translateText({
    required String text,
    required String sourceLanguage,
    required String targetLanguage,
    String? context,
    String? domain,
    double temperature = 0.3,
    int topK = 40,
    int randomSeed = 1,
  }) async {
    if (!_isModelLoaded) {
      throw Exception('Gemma-3N model not loaded. Call loadModel() first.');
    }

    try {
      final startTime = DateTime.now();

      if (kDebugMode) {
        print('🔄 Translating text with native Gemma-3N...');
        print('📝 Text: ${text.length > 50 ? '${text.substring(0, 50)}...' : text}');
        print('🌍 From: $sourceLanguage → To: $targetLanguage');
      }

      // Create optimized prompt for Gemma-3N
      final prompt = _buildOptimizedPrompt(
        text: text,
        sourceLanguage: sourceLanguage,
        targetLanguage: targetLanguage,
        context: context,
        domain: domain,
      );

      // Create session for translation
      final session = await _inferenceModel!.createSession(
        temperature: temperature,
        randomSeed: randomSeed,
        topK: topK,
      );

      try {
        // Add query and get response
        await session.addQueryChunk(Message.text(text: prompt, isUser: true));
        final response = await session.getResponse();

        // Clean up the response
        final cleanedTranslation = _cleanTranslationResponse(response);

        final processingTime = DateTime.now().difference(startTime);

        if (kDebugMode) {
          print('✅ Native translation completed!');
          print('⏱️ Processing time: ${processingTime.inMilliseconds}ms');
          print('🎯 Result: ${cleanedTranslation.length > 50 ? '${cleanedTranslation.substring(0, 50)}...' : cleanedTranslation}');
        }

        return TranslationResult(
          originalText: text,
          translatedText: cleanedTranslation,
          sourceLanguage: sourceLanguage,
          targetLanguage: targetLanguage,
          confidence: 0.95, // Native models generally have high confidence
          timestamp: DateTime.now(),
          processingTime: processingTime,
          metadata: {
            'model': _currentModelType,
            'service': 'flutter_gemma_native',
            'backend': _preferredBackend.name,
            'platform': defaultTargetPlatform.name,
            'native': true,
            'multimodal_capable': true,
            'temperature': temperature,
            'top_k': topK,
            'random_seed': randomSeed,
          },
        );
      } finally {
        // Always close the session
        await session.close();
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Native translation failed: $e');
      }
      rethrow;
    }
  }

  /// Translate image using native Gemma-3N multimodal capabilities
  Future<TranslationResult> translateImage({
    required Uint8List imageBytes,
    required String targetLanguage,
    String? sourceLanguage,
    String? context,
    double temperature = 0.3,
    int topK = 40,
    int randomSeed = 1,
  }) async {
    if (!_isModelLoaded) {
      throw Exception('Gemma-3N model not loaded. Call loadModel() first.');
    }

    try {
      final startTime = DateTime.now();

      if (kDebugMode) {
        print('🔄 Translating image with native Gemma-3N...');
        print('🖼️ Image size: ${imageBytes.length} bytes');
        print('🌍 Target language: $targetLanguage');
      }

      // Create optimized multimodal prompt
      final prompt = _buildImageTranslationPrompt(
        targetLanguage: targetLanguage,
        sourceLanguage: sourceLanguage,
        context: context,
      );

      // Create session for multimodal translation
      final session = await _inferenceModel!.createSession(
        temperature: temperature,
        randomSeed: randomSeed,
        topK: topK,
      );

      try {
        // Add image message
        await session.addQueryChunk(Message.withImage(
          text: prompt,
          imageBytes: imageBytes,
          isUser: true,
        ));

        final response = await session.getResponse();

        // Parse and clean the response
        final translationData = _parseImageTranslationResponse(response);

        final processingTime = DateTime.now().difference(startTime);

        if (kDebugMode) {
          print('✅ Native image translation completed!');
          print('⏱️ Processing time: ${processingTime.inMilliseconds}ms');
        }

        return TranslationResult(
          originalText: translationData['extracted_text'] ?? 'Text extracted from image',
          translatedText: translationData['translated_text'] ?? response,
          sourceLanguage: translationData['detected_language'] ?? sourceLanguage ?? 'auto',
          targetLanguage: targetLanguage,
          confidence: 0.95,
          timestamp: DateTime.now(),
          processingTime: processingTime,
          metadata: {
            'model': _currentModelType,
            'service': 'flutter_gemma_native',
            'backend': _preferredBackend.name,
            'platform': defaultTargetPlatform.name,
            'native': true,
            'multimodal': true,
            'temperature': temperature,
            'top_k': topK,
            'random_seed': randomSeed,
            'extracted_text': translationData['extracted_text'],
            'detected_language': translationData['detected_language'],
          },
        );
      } finally {
        // Always close the session
        await session.close();
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Native image translation failed: $e');
      }
      rethrow;
    }
  }

  /// Build optimized prompt for Gemma-3N text translation
  String _buildOptimizedPrompt({
    required String text,
    required String sourceLanguage,
    required String targetLanguage,
    String? context,
    String? domain,
  }) {
    // Optimized prompt structure for native Gemma-3N
    final buffer = StringBuffer();

    buffer.write('Translate from $sourceLanguage to $targetLanguage: ');

    if (context != null && context.isNotEmpty) {
      buffer.write('[Context: $context] ');
    }

    if (domain != null && domain.isNotEmpty) {
      buffer.write('[Domain: $domain] ');
    }

    buffer.write(text);

    return buffer.toString();
  }

  /// Build optimized prompt for image translation
  String _buildImageTranslationPrompt({
    required String targetLanguage,
    String? sourceLanguage,
    String? context,
  }) {
    final buffer = StringBuffer();

    buffer.write('Extract and translate text to $targetLanguage');

    if (sourceLanguage != null && sourceLanguage != 'auto') {
      buffer.write(' from $sourceLanguage');
    }

    if (context != null && context.isNotEmpty) {
      buffer.write(' [$context]');
    }

    buffer.write(': ');

    return buffer.toString();
  }

  /// Clean translation response from Gemma-3N
  String _cleanTranslationResponse(String response) {
    String cleaned = response.trim();

    // Remove common prefixes and suffixes
    cleaned = cleaned.replaceAll(RegExp(r'^(Translation:|Tradução:|Translated text:|Texto traduzido:)\s*', caseSensitive: false), '');
    cleaned = cleaned.replaceAll(RegExp(r'^(Here is the translation:|Aqui está a tradução:)\s*', caseSensitive: false), '');

    // Remove quotes if the entire response is quoted
    if (cleaned.startsWith('"') && cleaned.endsWith('"')) {
      cleaned = cleaned.substring(1, cleaned.length - 1);
    }

    return cleaned.trim();
  }

  /// Parse image translation response
  Map<String, String> _parseImageTranslationResponse(String response) {
    final result = <String, String>{};

    // For now, treat the entire response as translated text
    // In the future, we could implement more sophisticated parsing
    result['translated_text'] = _cleanTranslationResponse(response);

    return result;
  }

  /// Check if service is healthy
  Future<bool> isHealthy() async {
    return _isInitialized && _isModelLoaded;
  }

  /// Get model information
  Map<String, dynamic> getModelInfo() {
    return {
      'initialized': _isInitialized,
      'model_loaded': _isModelLoaded,
      'model_type': _currentModelType,
      'backend': _preferredBackend.name,
      'platform': defaultTargetPlatform.name,
      'native': true,
      'multimodal_capable': true,
    };
  }

  /// Dispose resources
  Future<void> dispose() async {
    try {
      if (_inferenceModel != null) {
        await _inferenceModel!.close();
        _inferenceModel = null;
      }

      _isInitialized = false;
      _isModelLoaded = false;
      _currentModelType = '';

      if (kDebugMode) {
        print('🗑️ Flutter Gemma Service disposed');
      }
    } catch (e) {
      if (kDebugMode) {
        print('⚠️ Error disposing Flutter Gemma Service: $e');
      }
    }
  }
}
